import pandas as pd

df = pd.read_excel('数据探索验证集.xlsx')
print(len(df))
df = df[df['intent'] != 0]
print(len(df))


def question_clean(query):
    for key in ['问题：', '提问：']:
        if key in query:
            return query.split(key)[-1].strip()
    return query.strip()


query = df['user_query'].apply(lambda x: question_clean(x))
dic = {0: "未知", 1: "cust_insight_agent", 2: "visit_agent", 3: "product_analyze_agent", 4: "product_analyze_agent"}
intent = df['intent'].apply(lambda x: dic[x])

for q, i in zip(query, intent):
    print(q, i)
