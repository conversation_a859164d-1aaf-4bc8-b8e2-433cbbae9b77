from annlp import LLM

llm = LLM(model='cloud1_deepseek-v3')

with open('config.xml') as file:
    agents = file.read().strip()


def intent(history):
    prompt = f"""请基于用户的历史聊天记录，判断应该使用哪一个Agent来解决用户的问题
# Agent列表
{agents}


# 历史聊天记录
{history}

# 输出格式
按照xml格式输出
<Reason>判断的理由,以第一人称输出理由，例如：您的问题涉及域名无法访问，将为您执行域名Agent解决</Reason>
<NameAgent Name</Name>
<ID>Agent Name</ID>
"""
    print(prompt)
    print('-' * 100)
    message = [{"role": "user", "content": prompt}]
    res = llm.gpt(message)
    print(res)
    return res


if __name__ == '__main__':
    # clear()

    while 1:
        query = input('请输入问题：')
        question = """user:备案失败
        agent：请输入备案号
        user：cvm怎么登录"""
        intent(query)
