import os
import gradio as gr
import pandas as pd
from annlp import sigmoid
from api import Search, Copilot, df2str

test_env = False
# search = Search(test_env=test_env, sort_weight_path='long-bias.pth')
search = Search(test_env=test_env)
llm = Copilot(model='hunyuan', verbose=True)


def search_func(query, env,
                faq_embedding_count, doc_embedding_count,
                faq_embedding_threshold, doc_embedding_threshold,
                faq_es_count, doc_es_count,
                sort_count, sort_threshold,
                channel):
    if env == '正式':
        search.set_env(False)
    else:
        search.set_env(True)
    faq_embedding_df = search.search(query, faq_embedding_count,
                                     data_type=1, engine_type='vector', channel=channel,
                                     threshold=faq_embedding_threshold)
    doc_embedding_df = search.search(query, doc_embedding_count,
                                     data_type=2, engine_type='vector', channel=channel,
                                     threshold=doc_embedding_threshold)
    faq_es_df = search.search(query, faq_es_count, data_type=1, engine_type='es', channel=channel)
    doc_es_df = search.search(query, doc_es_count, data_type=2, engine_type='es', channel=channel)

    # faq去重 faq的要根据前三位的id做过滤
    faq_df = pd.concat([faq_embedding_df, faq_es_df], axis=0)
    faq_df['source_id_new'] = faq_df['source_id'].apply(lambda x: '-'.join(x.split('-')[:3]))
    faq_df.drop_duplicates(subset=['source_id_new'], inplace=True)
    faq_df.drop(columns=['source_id_new'], inplace=True)
    # doc去重
    doc_df = pd.concat([doc_embedding_df, doc_es_df], axis=0)
    doc_df.drop_duplicates(subset=['source_id'], inplace=True)

    # 合并排序
    try:
        all_df = pd.concat([faq_df, doc_df], axis=0)
        logits = search.l2r(query, all_df['content'].tolist())
        all_df['score'] = sigmoid(logits)
        all_df = all_df.sort_values(by='score', ascending=False)
        all_df = all_df[:sort_count]
    except Exception as e:
        print(e)
        all_df = None

    search_res = search.search(query, count=sort_count, channel=channel,
                               threshold=faq_embedding_threshold,
                               sort_score=sort_threshold)

    # intent_logits = intent(query)

    return df2str(faq_embedding_df), df2str(doc_embedding_df), df2str(faq_es_df), df2str(doc_es_df), \
        df2str(all_df), df2str(search_res, inject_content=True), "", ""


def gpt_aug(query,
            env='正式',
            system_prompt=None,
            channel='售后',
            model='hunyuan',
            sort_score=0.1):
    test_env = False if env == '正式' else True
    return llm.gpt_aug(query,
                       test_env=test_env,
                       system_prompt=system_prompt,
                       channel=channel,
                       model=model,
                       sort_score=sort_score)


with gr.Blocks() as demo:
    with gr.Row():
        env = gr.Dropdown(choices=['正式', '测试'],
                          value='正式',
                          label='环境')
        channel = gr.Dropdown(choices=['售后', '智能客服', '微信支付', '磐石', '海外'],
                              value='售后',
                              label='渠道')
    with gr.TabItem('Search'):
        with gr.Row():
            with gr.Column():
                faq_embedding_count = gr.Slider(0, 20, value=10, label='faq embedding召回数', step=1)
                doc_embedding_count = gr.Slider(0, 20, value=10, label='doc embedding召回数', step=1)
            with gr.Column():
                faq_embedding_threshold = gr.Slider(0, 1, value=0.6, label='faq embedding阈值', step=0.1)
                doc_embedding_threshold = gr.Slider(0, 1, value=0.6, label='doc embedding阈值', step=0.1)
            with gr.Column():
                faq_es_count = gr.Slider(0, 20, value=10, label='faq es 召回数', step=1)
                doc_es_count = gr.Slider(0, 20, value=10, label='doc es 召回数', step=1)
            with gr.Column():
                sort_count = gr.Slider(0, 20, value=5, label='排序条数', step=1)
                sort_threshold = gr.Slider(0, 1, value=0.3, label='排序阈值', step=0.1)

        max_lines = 15
        query = gr.Textbox(label="query")
        btn = gr.Button("search")
        intent_res = gr.Textbox(label="闲聊检测（0闲聊，1垂类问题，2小店类目，3寒暄）")
        with gr.Row():
            output1 = gr.Textbox(label='faq embedding召回结果', max_lines=max_lines)
            output2 = gr.Textbox(label="doc embedding召回结果", max_lines=max_lines)
        with gr.Row():
            output3 = gr.Textbox(label='faq ES召回结果', max_lines=max_lines)
            output4 = gr.Textbox(label="doc ES召回结果", max_lines=max_lines)
        with gr.Row():
            output6 = gr.Textbox(label="e2e检索结果", max_lines=max_lines)
        with gr.Row():
            output5 = gr.Textbox(label="排序结果", max_lines=max_lines)
            output7 = gr.Textbox(label="过滤实体后", max_lines=max_lines)

        btn.click(fn=search_func,
                  inputs=[query, env,
                          faq_embedding_count, doc_embedding_count,
                          faq_embedding_threshold, doc_embedding_threshold,
                          faq_es_count, doc_es_count,
                          sort_count, sort_threshold,
                          channel],
                  outputs=[output1, output2, output3, output4, output5, output6, output7, intent_res])

    with gr.TabItem("RAG"):
        with gr.Row():
            clear_btn = gr.Button('清空上下文')
            model = gr.Dropdown(choices=['hunyuan', 'gpt-4o', 'deepseek-r1-0528', 'cloud1_deepseek-v3'],
                                value='cloud1_deepseek-v3', label='模型')
        system_prompt = gr.Textbox(label="system prompt", value=llm.system_prompt, visible=False)
        query = gr.Textbox(label="query")

        btn = gr.Button("问答")
        output0 = gr.Textbox(label="改写后的问题")
        output1 = gr.Textbox(label="带注入的prompt")
        output2 = gr.Markdown(label="答案")

        clear_btn.click(fn=llm.clear_context)
        btn.click(fn=gpt_aug,
                  inputs=[query, env, system_prompt, channel, model, sort_threshold],
                  outputs=[output0, output1, output2])

    with gr.TabItem("意图识别"):
        from intent import intent

        query = gr.Textbox(label="query", lines=5,
                           placeholder="多轮格式如下：\nuse：我的服务器无法登录\nassistant：请提供您的实例id\nuser：insdsf123456")
        btn = gr.Button("意图识别")
        answer = gr.Textbox(label="结果")
        btn.click(fn=intent,
                  inputs=[query],
                  outputs=[answer])

    # with gr.TabItem("DeepSearch"):
    #     query = gr.Textbox(label="query")
    #     btn = gr.Button("问答")
    #     think = gr.Textbox(label="思考过程", max_lines=max_lines)
    #     answer = gr.Markdown(label="最终答案")
    #     btn.click(fn=deep_search,
    #               inputs=[query],
    #               outputs=[think, answer])

demo.launch(server_port=8083, server_name='0.0.0.0', root_path='/copilot')
